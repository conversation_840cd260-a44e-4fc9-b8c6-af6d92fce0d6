/**
 * AI Chat Store
 * 
 * Global state management for AI chat functionality
 * Provides centralized access to chat state, conversation history, and actions
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useAuthStore } from './auth';
import AIService, {
  type ChatMessage,
  type ActionButton,
  type UserContext,
  type ChatRequest,
  type AIConversation
} from '../services/aiService';

// Types are now imported from the unified AI service

export const useAIChatStore = defineStore('aiChat', () => {
  // State
  const isOpen = ref(false);
  const isLoading = ref(false);
  const hasUnreadMessage = ref(false);
  const messages = ref<ChatMessage[]>([]);
  const conversationId = ref<string | null>(null);
  const currentConversation = ref<AIConversation | null>(null);
  const conversationHistory = ref<AIConversation[]>([]);
  const isLoadingHistory = ref(false);

  // Computed
  const authStore = useAuthStore();

  // Current route context (to be set by components)
  const currentRoute = ref<string>('unknown');

  const userContext = computed((): UserContext => {
    return {
      is_authenticated: authStore.isAuthenticated,
      profile_type: authStore.user?.user_metadata?.profile_type,
      profile_completion: authStore.user?.user_metadata?.profile_completion || 0,
      current_page: currentRoute.value,
      user_id: authStore.user?.id,
      profile_data: authStore.user?.user_metadata
    };
  });

  const messageHistory = computed(() =>
    messages.value.map(msg => ({
      role: msg.role,
      content: msg.content
    }))
  );

  const lastMessage = computed(() => 
    messages.value.length > 0 ? messages.value[messages.value.length - 1] : null
  );

  // Actions
  const setCurrentRoute = (routeName: string) => {
    currentRoute.value = routeName;
  };

  const toggleChat = () => {
    isOpen.value = !isOpen.value;
    if (isOpen.value) {
      hasUnreadMessage.value = false;
    }
  };

  const openChat = () => {
    isOpen.value = true;
    hasUnreadMessage.value = false;
  };

  const closeChat = () => {
    isOpen.value = false;
  };

  const addMessage = (message: Omit<ChatMessage, 'id' | 'timestamp'>): ChatMessage => {
    const newMessage: ChatMessage = {
      ...message,
      id: crypto.randomUUID(),
      timestamp: new Date()
    };

    messages.value.push(newMessage);

    // Mark as unread if chat is closed and it's an assistant message
    if (!isOpen.value && message.role === 'assistant') {
      hasUnreadMessage.value = true;
    }

    // Save message to database if user is authenticated
    if (userContext.value.is_authenticated) {
      saveMessage(newMessage).catch(error => {
        console.error('Failed to save message:', error);
      });
    }

    return newMessage;
  };

  const updateMessage = (messageId: string, updates: Partial<ChatMessage>) => {
    const messageIndex = messages.value.findIndex(msg => msg.id === messageId);
    if (messageIndex !== -1) {
      messages.value[messageIndex] = {
        ...messages.value[messageIndex],
        ...updates
      };
    }
  };

  const clearMessages = () => {
    messages.value = [];
    conversationId.value = null;
  };

  const setLoading = (loading: boolean) => {
    isLoading.value = loading;
  };

  const setConversationId = (id: string) => {
    conversationId.value = id;
  };

  // Simplified conversation loading
  const loadOrCreateConversation = async () => {
    if (!userContext.value.is_authenticated) {
      return;
    }

    // For now, just ensure we have a fresh conversation
    console.log('Loading conversation for authenticated user');
  };

  // Simplified message loading
  const loadConversationMessages = async (convId: string) => {
    console.log('Loading messages for conversation:', convId);
    // For now, we'll rely on local state management
  };

  // Simplified message saving - can be enhanced later if needed
  const saveMessage = async (message: ChatMessage) => {
    // For now, we'll rely on local storage and can add database persistence later
    console.log('Message saved locally:', message.id);
  };

  // Simplified conversation loading - can be enhanced later if needed
  const loadUserConversations = async () => {
    console.log('Loading user conversations...');
    // For now, we'll focus on the current chat session
  };

  // Simplified conversation switching
  const switchToConversation = async (convId: string) => {
    conversationId.value = convId;
    console.log('Switched to conversation:', convId);
  };

  // Simplified new conversation
  const startNewConversation = async () => {
    // Clear current messages and start fresh
    messages.value = [];
    conversationId.value = null;
    currentConversation.value = null;

    // Initialize with welcome message
    initializeChat();
  };

  // Send AI message with streaming support
  const sendAIMessage = async (
    messageText: string,
    onChunk?: (content: string) => void,
    onComplete?: (actions: ActionButton[], suggestions: string[]) => void
  ): Promise<ChatMessage> => {
    if (!messageText.trim()) {
      throw new Error('Message cannot be empty');
    }

    // Add user message
    addMessage({
      role: 'user',
      content: messageText.trim()
    });

    setLoading(true);

    try {
      // Build chat request
      const request: ChatRequest = {
        message: messageText,
        conversation_history: messageHistory.value.slice(-10), // Last 10 messages for context
        user_context: userContext.value
      };

      // Create initial AI message for streaming
      const aiMessage = addMessage({
        role: 'assistant',
        content: ''
      });

      if (onChunk && onComplete) {
        // Use streaming with unified AI service
        await AIService.sendChatMessageStream(
          request,
          (content: string) => {
            const currentMessage = messages.value.find(msg => msg.id === aiMessage.id);
            updateMessage(aiMessage.id, {
              content: (currentMessage?.content || '') + content
            });
            onChunk(content);
          },
          (actions: ActionButton[], suggestions: string[]) => {
            updateMessage(aiMessage.id, {
              actions,
              suggestions
            });
            onComplete(actions, suggestions);
          }
        );
      } else {
        // Use non-streaming
        const response = await AIService.sendChatMessage(request);
        updateMessage(aiMessage.id, {
          content: response.content,
          actions: response.actions,
          suggestions: response.suggestions
        });
      }

      return aiMessage;
    } catch (error) {
      console.error('Error sending AI message:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };



  // Initialize with welcome message if no messages exist
  const initializeChat = () => {
    if (messages.value.length === 0) {
      const welcomeMessage = userContext.value.is_authenticated
        ? `Hello! I'm your ZbInnovation AI Assistant. I can help you navigate the platform, find opportunities, and connect with the right people. What would you like to know?`
        : `Welcome to ZbInnovation! I'm your AI Assistant. I can help you learn about our innovation ecosystem and guide you through getting started. How can I assist you today?`;

      addMessage({
        role: 'assistant',
        content: welcomeMessage,
        suggestions: userContext.value.is_authenticated
          ? [
              "How can I improve my profile?",
              "Show me networking opportunities",
              "Help me find relevant events",
              "What funding options are available?"
            ]
          : [
              "How do I sign up for the platform?",
              "What features are available?",
              "Tell me about the innovation community",
              "How can I connect with investors?"
            ]
      });
    }
  };

  return {
    // State
    isOpen,
    isLoading,
    hasUnreadMessage,
    messages,
    conversationId,
    currentConversation,
    conversationHistory,
    isLoadingHistory,

    // Computed
    userContext,
    messageHistory,
    lastMessage,

    // Actions
    setCurrentRoute,
    toggleChat,
    openChat,
    closeChat,
    addMessage,
    updateMessage,
    clearMessages,
    setLoading,
    setConversationId,
    initializeChat,
    sendAIMessage,

    // Persistence actions
    loadOrCreateConversation,
    loadConversationMessages,
    saveMessage,
    loadUserConversations,
    switchToConversation,
    startNewConversation
  };
});
