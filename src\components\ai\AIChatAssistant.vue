<template>
  <div class="ai-chat-container" data-testid="ai-chat-container">
    <!-- Chat Toggle Button -->
    <q-btn
      v-if="!aiChatStore.isOpen"
      @click="toggleChat"
      fab
      color="primary"
      icon="psychology"
      class="chat-toggle-btn"
      :class="{
        'pulse': aiChatStore.hasUnreadMessage,
        'landing-position': isLandingPage,
        'community-position': isCommunityPage
      }"
      data-testid="ai-chat-toggle"
    >
      <q-tooltip class="bg-primary">AI Assistant</q-tooltip>
    </q-btn>

    <!-- Chat Window -->
    <q-card v-if="aiChatStore.isOpen" class="chat-window" :class="{
      'landing-position': isLandingPage,
      'community-position': isCommunityPage
    }">
      <!-- Header -->
      <q-card-section class="chat-header bg-primary text-white">
        <div class="row items-center justify-between">
          <div class="row items-center">
            <q-icon name="psychology" size="24px" class="q-mr-sm" />
            <div>
              <div class="text-weight-bold">ZbInnovation AI</div>
              <div class="text-caption">Your Innovation Assistant</div>
            </div>
          </div>
          <q-btn
            @click="closeChat"
            flat
            round
            icon="close"
            size="sm"
            class="text-white"
          />
        </div>
      </q-card-section>

      <!-- Messages Area -->
      <q-card-section class="chat-messages" ref="messagesContainer">
        <div v-if="aiChatStore.messages.length === 0" class="welcome-message">
          <q-icon name="waving_hand" size="32px" color="primary" class="q-mb-sm" />
          <p class="text-body1 q-mb-sm">
            Hello! I'm your ZbInnovation AI Assistant. I'm here to help you navigate the platform,
            connect with the right people, and grow your innovation journey.
          </p>
          <p class="text-body2 text-grey-7">
            Ask me about matchmaking, funding opportunities, or anything related to innovation in Zimbabwe!
          </p>

          <!-- Welcome suggestions -->
          <div v-if="welcomeSuggestions.length > 0" class="welcome-suggestions q-mt-md">
            <div class="text-caption text-grey-6 q-mb-xs">Try asking:</div>
            <div class="suggestion-chips">
              <q-chip
                v-for="suggestion in welcomeSuggestions"
                :key="suggestion"
                @click="sendSuggestion(suggestion)"
                clickable
                color="primary"
                text-color="white"
                size="sm"
                class="suggestion-chip"
              >
                {{ suggestion }}
              </q-chip>
            </div>
          </div>
        </div>

        <div
          v-for="(message, index) in aiChatStore.messages"
          :key="index"
          class="message"
          :class="{ 'user-message': message.role === 'user', 'ai-message': message.role === 'assistant' }"
          :data-testid="message.role === 'assistant' ? 'ai-message' : 'user-message'"
        >
          <div class="message-content">
            <div v-if="message.role === 'assistant'" class="ai-avatar">
              <q-icon name="smart_toy" size="20px" />
            </div>
            <div class="message-text" v-html="formatMessage(message.content)"></div>
            <div v-if="message.role === 'user'" class="user-avatar">
              <q-icon name="person" size="20px" />
            </div>
          </div>

          <!-- Action Buttons for AI messages -->
          <div v-if="message.role === 'assistant' && message.actions && message.actions.length > 0" class="message-actions q-mt-sm">
            <AIActionButton
              v-for="action in message.actions"
              :key="`${action.type}-${action.label}`"
              :action="action"
              variant="compact"
              @click="onActionClick(action)"
              @success="onActionSuccess(action)"
              @error="onActionError(action, $event)"
            />
          </div>

          <!-- Suggestions for AI messages -->
          <div v-if="message.role === 'assistant' && message.suggestions && message.suggestions.length > 0" class="message-suggestions q-mt-sm">
            <div class="text-caption text-grey-6 q-mb-xs">Quick questions:</div>
            <div class="suggestion-chips">
              <q-chip
                v-for="suggestion in message.suggestions"
                :key="suggestion"
                @click="sendSuggestion(suggestion)"
                clickable
                outline
                color="primary"
                size="sm"
                class="suggestion-chip"
              >
                {{ suggestion }}
              </q-chip>
            </div>
          </div>
        </div>

        <!-- Typing Indicator -->
        <div v-if="aiChatStore.isLoading" class="message ai-message">
          <div class="message-content">
            <div class="ai-avatar">
              <q-icon name="psychology" size="20px" />
            </div>
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </q-card-section>

      <!-- Input Area -->
      <q-card-section class="chat-input">
        <q-form @submit.prevent="() => sendMessage()" class="row q-gutter-sm">
          <q-input
            v-model="currentMessage"
            placeholder="Ask me anything about innovation..."
            outlined
            dense
            class="col"
            :disable="aiChatStore.isLoading"
            @keyup.enter="() => sendMessage()"
            data-testid="ai-chat-input"
          />
          <q-btn
            @click="() => sendMessage()"
            color="primary"
            icon="send"
            round
            :disable="!currentMessage.trim() || aiChatStore.isLoading"
            :loading="aiChatStore.isLoading"
            data-testid="ai-chat-send"
          />
        </q-form>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import { useRoute } from 'vue-router';
import { useAIChatStore } from '../../stores/aiChatStore';
import {
  type EnhancedChatMessage,
  type ActionButton,
  AIServiceError,
  buildDetailedUserContext,
  buildUserContext
} from '../../services/aiEnhancedService';
import AIActionButton from './AIActionButton.vue';

interface ChatMessage extends EnhancedChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  actions?: ActionButton[];
  suggestions?: string[];
}

// Composables
const $q = useQuasar();
const route = useRoute();
const aiChatStore = useAIChatStore();

// Local state
const currentMessage = ref('');
const messagesContainer = ref<HTMLElement>();

// Enhanced features
const welcomeSuggestions = ref<string[]>([]);

// Position adjustment based on current route
const needsPositionAdjustment = computed(() => {
  const currentPath = route.path;
  // Adjust position on landing page (news ticker) and community pages (create post button)
  return currentPath === '/' ||
         currentPath.includes('/virtual-community') ||
         currentPath.includes('/community');
});

// Check if we're on landing page (for news ticker positioning)
const isLandingPage = computed(() => {
  return route.path === '/';
});

// Check if we're on community page (for create post button positioning)
const isCommunityPage = computed(() => {
  return route.path.includes('/virtual-community') || route.path.includes('/community');
});

// Computed properties
const canSendMessage = computed(() => currentMessage.value.trim().length > 0 && !aiChatStore.isLoading);

// Methods
const toggleChat = () => {
  aiChatStore.toggleChat();
  if (aiChatStore.isOpen) {
    nextTick(() => {
      scrollToBottom();
    });
  }
};

const closeChat = () => {
  aiChatStore.closeChat();
};

const sendMessage = async (messageText?: string | Event) => {
  // Handle both string messages and form submit events
  let messageToSend: string;
  if (typeof messageText === 'string') {
    messageToSend = messageText;
  } else {
    // If it's an event or undefined, use the current message input
    messageToSend = currentMessage.value.trim();
  }

  if (!messageToSend || aiChatStore.isLoading) return;

  if (!messageText) currentMessage.value = '';

  await nextTick();
  scrollToBottom();

  try {
    console.log('Sending AI message with context:', aiChatStore.userContext);

    // Use the store's sendAIMessage method with streaming
    await aiChatStore.sendAIMessage(
      messageToSend,
      // onChunk callback
      (content: string) => {
        console.log('Received chunk:', content);
        nextTick(() => scrollToBottom());
      },
      // onComplete callback
      (actions: ActionButton[], suggestions: string[]) => {
        console.log('Streaming completed with actions:', actions, 'suggestions:', suggestions);

        if (!aiChatStore.isOpen) {
          aiChatStore.hasUnreadMessage = true;
        }
      }
    );

    console.log('Streaming AI response completed');

  } catch (error: any) {
    console.error('Enhanced AI Chat error:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      type: error.type,
      details: error.details
    });

    let errorContent = '';
    let errorTitle = 'AI Service Error';

    if (error instanceof AIServiceError) {
      // Handle structured AI service errors
      errorTitle = error.message;
      errorContent = `**${error.message}**\n\n`;

      if (error.details) {
        errorContent += `${error.details}\n\n`;
      }

      errorContent += `**What you can do:**\nPlease try again in a few moments. If this problem continues, contact our support team.`;

      // Show specific error type in development
      if (process.env.NODE_ENV === 'development') {
        errorContent += `\n\n*Error Type: ${error.type}*`;
      }
    } else {
      // Handle unexpected errors
      errorContent = `**AI Service Unavailable**\n\nWe're experiencing technical difficulties with our AI assistant.\n\n**What you can do:**\nPlease try again in a few moments. If this problem continues, contact our support team.`;

      if (process.env.NODE_ENV === 'development') {
        errorContent += `\n\n*Technical Details: ${error.message || 'Unknown error'}*`;
      }
    }

    aiChatStore.addMessage({
      role: 'assistant',
      content: errorContent,
      actions: [
        {
          type: 'action',
          label: 'Try Again',
          icon: 'refresh',
          action: 'retry-last-message',
          color: 'primary'
        },
        {
          type: 'navigation',
          label: 'Contact Support',
          icon: 'support_agent',
          url: '/contact-us',
          color: 'secondary'
        }
      ],
      suggestions: [
        "Try asking your question again",
        "Contact support for help",
        "Explore the platform manually"
      ]
    });

    // Show user-friendly notification
    $q.notify({
      type: 'negative',
      message: errorTitle,
      caption: 'AI assistant is temporarily unavailable',
      position: 'top',
      timeout: 5000,
      actions: [
        {
          label: 'Dismiss',
          color: 'white'
        }
      ]
    });
  } finally {
    await nextTick();
    scrollToBottom();
  }
};

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

const formatMessage = (content: string | any) => {
  // Force conversion to plain string to handle Vue Proxy objects
  let plainContent = '';

  try {
    if (typeof content === 'string') {
      plainContent = content;
    } else if (content && typeof content === 'object') {
      // If it's an event object, return empty string
      if (content.type && content.target) {
        return '';
      }

      // For Vue Proxy objects, try multiple approaches to get the raw value
      if (content.valueOf && typeof content.valueOf === 'function') {
        plainContent = String(content.valueOf());
      } else if (content.toString && typeof content.toString === 'function') {
        const stringValue = content.toString();
        if (stringValue && stringValue !== '[object Object]') {
          plainContent = stringValue;
        }
      } else {
        // Last resort: try to access the raw value
        plainContent = String(content);
      }

      // If we still have an object representation, return empty string
      if (plainContent === '[object Object]') {
        console.warn('formatMessage could not convert object to string:', content);
        return '';
      }
    } else {
      plainContent = String(content || '');
    }
  } catch (e) {
    console.warn('formatMessage error converting content:', e);
    return '';
  }

  // Simple formatting for better readability
  return plainContent
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>');
};

// Enhanced methods for new features
const sendSuggestion = (suggestion: string) => {
  sendMessage(suggestion);
};

const onActionClick = (action: ActionButton) => {
  console.log('Action clicked:', action);

  // Handle special retry action
  if (action.action === 'retry-last-message') {
    // Find the last user message and resend it
    const lastUserMessage = [...aiChatStore.messages].reverse().find(msg => msg.role === 'user');
    if (lastUserMessage) {
      sendMessage(lastUserMessage.content);
    }
    return;
  }

  // Prevent chat from closing when action is clicked
  // The AIActionButton component will handle the actual execution
};

const onActionSuccess = (action: ActionButton) => {
  console.log('Action completed successfully:', action);
  $q.notify({
    type: 'positive',
    message: `${action.label} completed successfully`,
    position: 'top',
    timeout: 2000
  });
};

const onActionError = (action: ActionButton, error: Error) => {
  console.error('Action failed:', action, error);
  $q.notify({
    type: 'negative',
    message: `Failed to ${action.label.toLowerCase()}: ${error.message}`,
    position: 'top',
    timeout: 3000
  });
};

const initializeWelcomeSuggestions = async () => {
  // Set welcome suggestions based on user context
  const currentUserContext = aiChatStore.userContext;
  if (!currentUserContext.is_authenticated) {
    welcomeSuggestions.value = [
      "How do I sign up?",
      "What is ZbInnovation?",
      "Tell me about the community"
    ];
  } else {
    // Generate intelligent suggestions based on profile analysis
    try {
      // Build detailed context for profile suggestions
      let detailedContext;
      try {
        detailedContext = await buildDetailedUserContext(false, false, route.path);
      } catch (contextError) {
        console.warn('Failed to build detailed context during initialization, using basic context:', contextError);
        detailedContext = buildUserContext(route.path);
      }

      // Generate simple profile-based suggestions
      const topSuggestions = [];
      if (detailedContext.profile_type === 'startup') {
        topSuggestions.push("How can I find investors?", "Show me funding opportunities");
      } else if (detailedContext.profile_type === 'investor') {
        topSuggestions.push("Show me promising startups", "What are the latest investment trends?");
      } else {
        topSuggestions.push("How can I improve my profile?", "Show me networking opportunities");
      }

      welcomeSuggestions.value = [
        ...topSuggestions,
        "What should I do next?",
        "Show me my growth opportunities"
      ];
    } catch (error) {
      console.warn('Failed to generate profile suggestions:', error);
      welcomeSuggestions.value = [
        "How do I get started?",
        "Show me platform features",
        "Help me create content"
      ];
    }
  }
};

// Lifecycle
onMounted(async () => {
  try {
    // Load or create conversation for authenticated users
    if (aiChatStore.userContext.is_authenticated) {
      try {
        await aiChatStore.loadOrCreateConversation();
      } catch (conversationError) {
        console.warn('Failed to load conversation history, continuing with fresh chat:', conversationError);
        // Continue without conversation history - not critical for functionality
      }
    }

    // Initialize the AI chat store if no messages exist
    if (aiChatStore.messages.length === 0) {
      aiChatStore.initializeChat();
    }

    // Initialize welcome suggestions (async)
    try {
      await initializeWelcomeSuggestions();
    } catch (suggestionsError) {
      console.warn('Failed to load welcome suggestions, using defaults:', suggestionsError);
      // Set default suggestions as fallback
      welcomeSuggestions.value = [
        "How can I get started?",
        "What services do you offer?",
        "Tell me about the platform"
      ];
    }
  } catch (error) {
    console.error('Failed to initialize AI chat:', error);
    // Show user-friendly error message
    $q.notify({
      type: 'warning',
      message: 'AI Assistant initialization incomplete',
      caption: 'Some features may be limited',
      position: 'top',
      timeout: 3000
    });
  }
});

// Save chat history when messages change
const saveMessages = () => {
  localStorage.setItem('ai-chat-history', JSON.stringify(aiChatStore.messages));
};

// Watch for message changes to save history
import { watch } from 'vue';
watch(() => aiChatStore.messages, saveMessages, { deep: true });
</script>

<style scoped>
.ai-chat-container {
  position: fixed;
  bottom: 32px; /* Reduced by 10%: was 36px, now 32px */
  right: 4px; /* Reduced by 50%: was 8px, now 4px */
  z-index: 999999; /* Highest z-index to be above all components */
}

.chat-toggle-btn {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Position adjustments for landing page (above news ticker) */
.chat-toggle-btn.landing-position {
  bottom: 50px; /* Reduced by 10%: was 56px, now 50px */
  right: 0.1px; /* Reduced by 50%: was 0.2px, now 0.1px */
}

.chat-window.landing-position {
  bottom: 50px; /* Same positioning as button */
  right: 0.1px;
}

/* Position adjustments for community page (above create post button) */
.chat-toggle-btn.community-position {
  bottom: 46px; /* Reduced by 10%: was 51px, now 46px */
  right: 0.2px; /* Reduced by 50%: was 0.4px, now 0.2px */
}

.chat-window.community-position {
  bottom: 46px; /* Same positioning as button */
  right: 0.2px;
}

.chat-toggle-btn.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(25, 118, 210, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(25, 118, 210, 0);
  }
}

.chat-window {
  width: 380px;
  height: 500px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  overflow: hidden;
  z-index: 999999; /* Highest z-index to ensure chat window is above everything */
}

.chat-header {
  padding: 16px;
  border-radius: 0;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #f8f9fa;
}

.welcome-message {
  text-align: center;
  padding: 20px;
  color: #666;
}

.message {
  margin-bottom: 16px;
}

.message-content {
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.user-message .message-content {
  flex-direction: row-reverse;
}

.message-text {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 18px;
  line-height: 1.4;
}

.user-message .message-text {
  background: #1976d2;
  color: white;
  border-bottom-right-radius: 4px;
}

.ai-message .message-text {
  background: white;
  color: #333;
  border: 1px solid #e0e0e0;
  border-bottom-left-radius: 4px;
}

.ai-avatar, .user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.ai-avatar {
  background: #e3f2fd;
  color: #1976d2;
}

.user-avatar {
  background: #1976d2;
  color: white;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 18px;
  border-bottom-left-radius: 4px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #bbb;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.chat-input {
  padding: 16px;
  background: white;
  border-top: 1px solid #e0e0e0;
}

/* Enhanced features styles */
.welcome-suggestions {
  margin-top: 16px;
}

.message-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
  padding-left: 32px; /* Align with message content */
}

.message-suggestions {
  margin-top: 8px;
  padding-left: 32px; /* Align with message content */
}

.suggestion-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 4px;
}

.suggestion-chip {
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-chip:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-message .suggestion-chips .suggestion-chip {
  background: rgba(25, 118, 210, 0.1);
  color: #1976d2;
}

.welcome-message .suggestion-chips .suggestion-chip:hover {
  background: rgba(25, 118, 210, 0.2);
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  .ai-chat-container {
    bottom: 25px; /* Reduced by 10%: was 28px, now 25px */
    right: 2px; /* Reduced by 50%: was 4px, now 2px */
  }

  .chat-toggle-btn.landing-position {
    bottom: 48px; /* Reduced by 10%: was 53px, now 48px */
    right: 0.1px; /* Reduced by 50%: was 0.2px, now 0.1px */
  }

  .chat-toggle-btn.community-position {
    bottom: 44px; /* Reduced by 10%: was 49px, now 44px */
    right: 0.15px; /* Reduced by 50%: was 0.3px, now 0.15px */
  }

  .chat-window {
    width: calc(100vw - 20px);
    height: calc(100vh - 120px);
    bottom: 25px; /* Reduced by 10%: was 28px, now 25px */
    right: 2px; /* Reduced by 50%: was 4px, now 2px */
  }

  .chat-window.landing-position {
    bottom: 48px; /* Match button position */
    right: 0.1px;
    height: calc(100vh - 133px); /* Adjust height for mobile with new positioning */
  }

  .chat-window.community-position {
    bottom: 44px; /* Match button position */
    right: 0.15px;
    height: calc(100vh - 129px); /* Adjust height for mobile community page */
  }
}
</style>
