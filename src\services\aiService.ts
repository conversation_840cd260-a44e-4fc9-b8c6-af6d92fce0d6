/**
 * Unified AI Service
 * 
 * Consolidated AI service that combines chat functionality, conversation persistence,
 * and enhanced context awareness in a single, clean interface.
 */

import { supabase } from '../lib/supabase';
import { triggerAuthDialog } from './authDialogService';
import { validateRouteForCTA, logRouteValidationFailure, isRouteSafe } from './routeValidationService';

// Core Types
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  actions?: ActionButton[];
  suggestions?: string[];
}

export interface ActionButton {
  type: 'navigation' | 'action' | 'external';
  label: string;
  icon?: string;
  action?: string;
  url?: string;
  color?: string;
  route?: string;
}

export interface UserContext {
  is_authenticated: boolean;
  profile_type?: string;
  profile_completion?: number;
  current_page: string;
  user_id?: string;
  profile_data?: any;
}

export interface ChatRequest {
  message: string;
  conversation_history: Array<{ role: string; content: string }>;
  user_context: UserContext;
}

export interface ChatResponse {
  response: string;
  actions?: ActionButton[];
  suggestions?: string[];
  conversation_id?: string;
  error?: string;
}

// Conversation Persistence Types
export interface AIConversation {
  id: string;
  user_id: string;
  title?: string;
  summary?: string;
  context_data: Record<string, any>;
  message_count: number;
  last_message_at?: string;
  created_at: string;
  updated_at: string;
}

export interface AIMessage {
  id: string;
  conversation_id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata: Record<string, any>;
  actions: any[];
  suggestions: any[];
  created_at: string;
}

// Error Handling
export class AIServiceError extends Error {
  constructor(
    message: string,
    public type: string = 'unknown',
    public details?: string
  ) {
    super(message);
    this.name = 'AIServiceError';
  }
}

/**
 * Unified AI Service Class
 */
export class AIService {
  
  /**
   * Send a chat message with streaming support
   */
  static async sendChatMessage(
    request: ChatRequest,
    onChunk?: (content: string) => void,
    onComplete?: (actions: ActionButton[], suggestions: string[]) => void
  ): Promise<ChatMessage> {
    try {
      console.log('Sending AI chat message:', request);

      if (onChunk) {
        // Use streaming
        await this.sendChatMessageStream(request, onChunk, onComplete || (() => {}));
        
        // Return the last message from the response
        const lastMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: '', // Will be filled by streaming
          timestamp: new Date(),
          actions: [],
          suggestions: []
        };
        
        return lastMessage;
      } else {
        // Use regular request
        const { data, error } = await supabase.functions.invoke('ai-enhanced-chat', {
          body: request
        });

        if (error) {
          throw new AIServiceError(`AI service error: ${error.message}`, 'service_error');
        }

        const response: ChatResponse = data;
        
        return {
          id: Date.now().toString(),
          role: 'assistant',
          content: response.response,
          timestamp: new Date(),
          actions: response.actions || [],
          suggestions: response.suggestions || []
        };
      }
    } catch (error: any) {
      console.error('AI Service Error:', error);
      throw new AIServiceError(
        error.message || 'Failed to send chat message',
        error.type || 'unknown',
        error.details
      );
    }
  }

  /**
   * Send chat message with streaming
   */
  static async sendChatMessageStream(
    request: ChatRequest,
    onChunk: (content: string) => void,
    onComplete: (actions: ActionButton[], suggestions: string[]) => void
  ): Promise<void> {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      const token = session?.access_token || supabase.supabaseKey;

      const response = await fetch(`${supabase.supabaseUrl}/functions/v1/ai-enhanced-chat-stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        throw new AIServiceError(`HTTP ${response.status}: ${response.statusText}`, 'http_error');
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new AIServiceError('No response body available', 'stream_error');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let actions: ActionButton[] = [];
      let suggestions: string[] = [];

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line);
              
              if (data.type === 'content' && data.content) {
                onChunk(data.content);
              } else if (data.type === 'complete') {
                actions = data.actions || [];
                suggestions = data.suggestions || [];
              }
            } catch (parseError) {
              console.warn('Failed to parse streaming data:', parseError);
            }
          }
        }
      }

      onComplete(actions, suggestions);
    } catch (error: any) {
      console.error('Streaming AI Service Error:', error);
      throw new AIServiceError(
        error.message || 'Failed to stream chat message',
        error.type || 'stream_error'
      );
    }
  }

  /**
   * Create a new conversation
   */
  static async createConversation(
    userId: string,
    title?: string,
    contextData: Record<string, any> = {}
  ): Promise<AIConversation> {
    const { data, error } = await supabase
      .from('ai_conversations')
      .insert({
        user_id: userId,
        title,
        context_data: contextData
      })
      .select()
      .single();

    if (error) {
      throw new AIServiceError(`Failed to create conversation: ${error.message}`);
    }

    return data;
  }

  /**
   * Get user conversations
   */
  static async getUserConversations(userId: string, limit = 10): Promise<AIConversation[]> {
    const { data, error } = await supabase
      .from('ai_conversations')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw new AIServiceError(`Failed to get conversations: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Add message to conversation
   */
  static async addMessage(
    conversationId: string,
    role: 'user' | 'assistant' | 'system',
    content: string,
    options: {
      metadata?: Record<string, any>;
      actions?: any[];
      suggestions?: any[];
    } = {}
  ): Promise<AIMessage> {
    const { data, error } = await supabase
      .from('ai_messages')
      .insert({
        conversation_id: conversationId,
        role,
        content,
        metadata: options.metadata || {},
        actions: options.actions || [],
        suggestions: options.suggestions || []
      })
      .select()
      .single();

    if (error) {
      throw new AIServiceError(`Failed to add message: ${error.message}`);
    }

    return data;
  }

  /**
   * Get conversation messages
   */
  static async getConversationMessages(conversationId: string): Promise<AIMessage[]> {
    const { data, error } = await supabase
      .from('ai_messages')
      .select('*')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true });

    if (error) {
      throw new AIServiceError(`Failed to get messages: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get or create conversation for user
   */
  static async getOrCreateUserConversation(
    userId: string,
    contextData: Record<string, any> = {}
  ): Promise<AIConversation> {
    const conversations = await this.getUserConversations(userId, 1);

    if (conversations.length > 0) {
      const lastConversation = conversations[0];
      const lastMessageTime = lastConversation.last_message_at
        ? new Date(lastConversation.last_message_at)
        : new Date(lastConversation.created_at);

      const hoursSinceLastMessage = (Date.now() - lastMessageTime.getTime()) / (1000 * 60 * 60);

      if (hoursSinceLastMessage < 24) {
        return lastConversation;
      }
    }

    return this.createConversation(userId, undefined, contextData);
  }

  /**
   * Execute an action button
   */
  static async executeAction(action: ActionButton): Promise<void> {
    console.log('Executing action:', action);

    try {
      switch (action.type) {
        case 'navigation':
          if (action.route && isRouteSafe(action.route)) {
            // Handle navigation
            window.location.hash = action.route;
          }
          break;
        case 'action':
          await this.handlePlatformAction(action.action!);
          break;
        case 'external':
          if (action.url) {
            window.open(action.url, '_blank');
          }
          break;
        default:
          console.warn('Unknown action type:', action.type);
      }
    } catch (error) {
      console.error('Error executing action:', error);
      throw error;
    }
  }

  /**
   * Handle platform-specific actions
   */
  private static async handlePlatformAction(actionType: string): Promise<void> {
    switch (actionType) {
      case 'login':
      case 'signup':
        triggerAuthDialog(actionType);
        break;
      case 'complete-profile':
        window.location.hash = '/profile/edit';
        break;
      case 'create-post':
        window.location.hash = '/virtual-community';
        break;
      default:
        console.warn('Unknown platform action:', actionType);
    }
  }
}

// Export default instance
export default AIService;
